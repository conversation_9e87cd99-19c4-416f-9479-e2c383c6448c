{"version": 3, "file": "LogBoxSymbolication.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/Data/LogBoxSymbolication.tsx"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;AAIH,6FAAqE;AAQrE,MAAM,KAAK,GAAgD,IAAI,GAAG,EAAE,CAAC;AAErE;;GAEG;AACH,MAAM,QAAQ,GAAG,CAAC,EAChB,KAAK,EAAE,UAAU,EACjB,SAAS,GACc,EAA0B,EAAE;IACnD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;KACnD;IACD,MAAM,KAAK,GAAiB,EAAE,CAAC;IAC/B,KAAK,MAAM,UAAU,IAAI,UAAU,EAAE;QACnC,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,UAAU,IAAI,UAAU,EAAE;YAC5B,IAAI,OAAO,UAAU,CAAC,QAAQ,KAAK,SAAS,EAAE;gBAC5C,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;aACrE;YACD,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;SAChC;QACD,KAAK,CAAC,IAAI,CAAC;YACT,SAAS,EAAE,EAAE;YACb,MAAM,EAAE,UAAU,CAAC,MAAM;YACzB,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,UAAU,EAAE,UAAU,CAAC,UAAU;YACjC,UAAU,EAAE,UAAU,CAAC,UAAU;YACjC,QAAQ;SACT,CAAC,CAAC;KACJ;IACD,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC9B,CAAC,CAAC;AAEF,SAAgB,WAAW,CAAC,KAAY;IACtC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACtB,CAAC;AAFD,kCAEC;AAED,SAAgB,WAAW,CAAC,KAAY;IACtC,IAAI,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,OAAO,IAAI,IAAI,EAAE;QACnB,OAAO,GAAG,IAAA,+BAAqB,EAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtD,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;KAC3B;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AARD,kCAQC", "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport { StackFrame as UpstreamStackFrame } from 'stacktrace-parser';\n\nimport symbolicateStackTrace from '../modules/symbolicateStackTrace';\n\ntype SymbolicatedStackTrace = any;\n\ntype StackFrame = UpstreamStackFrame & { collapse?: boolean };\n\nexport type Stack = StackFrame[];\n\nconst cache: Map<Stack, Promise<SymbolicatedStackTrace>> = new Map();\n\n/**\n * Sanitize because sometimes, `symbolicateStackTrace` gives us invalid values.\n */\nconst sanitize = ({\n  stack: maybeStack,\n  codeFrame,\n}: SymbolicatedStackTrace): SymbolicatedStackTrace => {\n  if (!Array.isArray(maybeStack)) {\n    throw new Error('Expected stack to be an array.');\n  }\n  const stack: StackFrame[] = [];\n  for (const maybeFrame of maybeStack) {\n    let collapse = false;\n    if ('collapse' in maybeFrame) {\n      if (typeof maybeFrame.collapse !== 'boolean') {\n        throw new Error('Expected stack frame `collapse` to be a boolean.');\n      }\n      collapse = maybeFrame.collapse;\n    }\n    stack.push({\n      arguments: [],\n      column: maybeFrame.column,\n      file: maybeFrame.file,\n      lineNumber: maybeFrame.lineNumber,\n      methodName: maybeFrame.methodName,\n      collapse,\n    });\n  }\n  return { stack, codeFrame };\n};\n\nexport function deleteStack(stack: Stack): void {\n  cache.delete(stack);\n}\n\nexport function symbolicate(stack: Stack): Promise<SymbolicatedStackTrace> {\n  let promise = cache.get(stack);\n  if (promise == null) {\n    promise = symbolicateStackTrace(stack).then(sanitize);\n    cache.set(stack, promise);\n  }\n\n  return promise;\n}\n"]}