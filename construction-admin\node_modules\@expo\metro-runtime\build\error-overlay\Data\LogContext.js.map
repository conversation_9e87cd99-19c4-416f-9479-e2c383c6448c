{"version": 3, "file": "LogContext.js", "sourceRoot": "", "sources": ["../../../src/error-overlay/Data/LogContext.tsx"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,+CAAwC;AAExC,2CAAwC;AAExC,wCAAwC;AAE3B,QAAA,UAAU,GAAG,eAAK,CAAC,aAAa,CAInC,IAAI,CAAC,CAAC;AAEhB,SAAgB,OAAO;IAKrB,MAAM,IAAI,GAAG,eAAK,CAAC,UAAU,CAAC,kBAAU,CAAC,CAAC;IAC1C,IAAI,CAAC,IAAI,EAAE;QACT,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;YAC1D,uEAAuE;YACvE,MAAM,yBAAyB,GAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAC;YAChF,IAAI,yBAAyB,EAAE,WAAW,EAAE;gBAC1C,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;gBAC9D,OAAO;oBACL,GAAG,GAAG;oBACN,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,IAAI,qBAAS,CAAC,GAAG,CAAC,CAAC;iBACrD,CAAC;aACH;SACF;QAED,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;KAC9D;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAtBD,0BAsBC;AAED,SAAgB,cAAc;IAC5B,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,CAAC;IAC7C,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAChC,CAAC;AAHD,wCAGC", "sourcesContent": ["import React from 'react';\nimport { Platform } from 'react-native';\n\nimport { LogBoxLog } from './LogBoxLog';\n\n// Context provider for Array<LogBoxLog>\n\nexport const LogContext = React.createContext<{\n  selectedLogIndex: number;\n  isDisabled: boolean;\n  logs: LogBoxLog[];\n} | null>(null);\n\nexport function useLogs(): {\n  selectedLogIndex: number;\n  isDisabled: boolean;\n  logs: LogBoxLog[];\n} {\n  const logs = React.useContext(LogContext);\n  if (!logs) {\n    if (Platform.OS === 'web' && typeof window !== 'undefined') {\n      // Logbox data that is pre-fetched on the dev server and rendered here.\n      const expoCliStaticErrorElement = document.getElementById('_expo-static-error');\n      if (expoCliStaticErrorElement?.textContent) {\n        const raw = JSON.parse(expoCliStaticErrorElement.textContent);\n        return {\n          ...raw,\n          logs: raw.logs.map((raw: any) => new LogBoxLog(raw)),\n        };\n      }\n    }\n\n    throw new Error('useLogs must be used within a LogProvider');\n  }\n  return logs;\n}\n\nexport function useSelectedLog() {\n  const { selectedLogIndex, logs } = useLogs();\n  return logs[selectedLogIndex];\n}\n"]}