{"name": "construction-admin", "version": "1.0.0", "description": "Construction Company Admin Mobile App", "main": "App.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "axios": "^1.6.2", "expo": "~50.0.0", "expo-constants": "~15.4.0", "expo-image-picker": "~14.7.1", "expo-secure-store": "~12.8.1", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-native": "0.73.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-toast-message": "^2.1.7", "react-native-web": "~0.19.6", "react-dom": "18.2.0", "@expo/metro-runtime": "~3.1.3"}, "devDependencies": {"@babel/core": "^7.20.0"}, "keywords": ["react-native", "expo", "construction", "admin", "mobile"], "author": "Construction Company", "license": "MIT", "private": true}