import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';

// Register the main component
AppRegistry.registerComponent('main', () => App);
AppRegistry.registerComponent(appName, () => App);

// For web compatibility
if (typeof document !== 'undefined') {
  AppRegistry.runApplication('main', {
    initialProps: {},
    rootTag: document.getElementById('root') || document.getElementById('main'),
  });
}
